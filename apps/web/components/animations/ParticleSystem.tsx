'use client';

import { useEffect, useRef } from 'react';
import { usePerformanceOptimization, useThrottledAnimation } from './hooks/usePerformanceOptimization';

export function ParticleSystem() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { shouldAnimate } = usePerformanceOptimization(canvasRef);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !shouldAnimate) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Optimized Particle class
    class Particle {
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      baseOpacity: number;
      color: string;
      pulseOffset: number;

      constructor() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.vx = (Math.random() - 0.5) * 0.3; // Reduced velocity
        this.vy = (Math.random() - 0.5) * 0.3;
        this.size = Math.random() * 1.5 + 0.5; // Smaller particles
        this.baseOpacity = Math.random() * 0.2 + 0.05; // Lower opacity
        this.opacity = this.baseOpacity;
        this.pulseOffset = Math.random() * Math.PI * 2;

        const colors = [
          '0, 212, 255',
          '57, 255, 20',
          '139, 69, 255',
          '255, 165, 0'
        ];
        this.color = colors[Math.floor(Math.random() * colors.length)];
      }

      update(time: number) {
        this.x += this.vx;
        this.y += this.vy;

        // Wrap around edges
        if (this.x < 0) this.x = canvas.width;
        if (this.x > canvas.width) this.x = 0;
        if (this.y < 0) this.y = canvas.height;
        if (this.y > canvas.height) this.y = 0;

        // Optimized opacity pulsing using sine wave
        this.opacity = this.baseOpacity + Math.sin(time * 0.001 + this.pulseOffset) * 0.1;
      }

      draw() {
        // Simplified drawing without shadow blur
        ctx.globalAlpha = this.opacity;
        ctx.fillStyle = `rgba(${this.color}, ${this.opacity})`;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
      }
    }

    // Drastically reduced particle count - max 20 particles
    const particles: Particle[] = [];
    const maxParticles = Math.min(20, Math.floor((canvas.width * canvas.height) / 80000));

    for (let i = 0; i < maxParticles; i++) {
      particles.push(new Particle());
    }

    // Use throttled animation hook for 24fps
    const animationCallback = (currentTime: number) => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        particle.update(currentTime);
        particle.draw();
      });
    };

    const { start, stop } = useThrottledAnimation(animationCallback, 24);
    start();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      stop();
    };
  }, [shouldAnimate]);

  // Don't render anything if shouldn't animate
  if (!shouldAnimate) {
    return null;
  }

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 pointer-events-none opacity-25"
      style={{ zIndex: 1 }}
    />
  );
}