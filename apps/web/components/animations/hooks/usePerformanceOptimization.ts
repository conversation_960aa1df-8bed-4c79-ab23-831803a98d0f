'use client';

import { useEffect, useState } from 'react';

interface PerformanceSettings {
  isVisible: boolean;
  reducedMotion: boolean;
  lowPowerMode: boolean;
  shouldAnimate: boolean;
}

export function usePerformanceOptimization(elementRef: React.RefObject<Element | null>): PerformanceSettings {
  const [isVisible, setIsVisible] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [lowPowerMode, setLowPowerMode] = useState(false);

  useEffect(() => {
    // Check for reduced motion preference
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setReducedMotion(motionQuery.matches);
    
    const handleMotionChange = (e: MediaQueryListEvent) => setReducedMotion(e.matches);
    motionQuery.addEventListener('change', handleMotionChange);

    // Check for low power mode (battery saver)
    const checkBatteryStatus = async () => {
      if ('getBattery' in navigator) {
        try {
          const battery = await (navigator as any).getBattery();
          setLowPowerMode(!battery.charging && battery.level < 0.2);
          
          battery.addEventListener('chargingchange', () => {
            setLowPowerMode(!battery.charging && battery.level < 0.2);
          });
          battery.addEventListener('levelchange', () => {
            setLowPowerMode(!battery.charging && battery.level < 0.2);
          });
        } catch (error) {
          // Battery API not supported, assume normal power mode
          setLowPowerMode(false);
        }
      }
    };
    checkBatteryStatus();

    // Intersection Observer for visibility
    const observer = new IntersectionObserver(
      ([entry]) => setIsVisible(entry.isIntersecting),
      { 
        threshold: 0.1,
        rootMargin: '50px' // Start loading slightly before visible
      }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => {
      motionQuery.removeEventListener('change', handleMotionChange);
      observer.disconnect();
    };
  }, [elementRef]);

  const shouldAnimate = isVisible && !reducedMotion && !lowPowerMode;

  return {
    isVisible,
    reducedMotion,
    lowPowerMode,
    shouldAnimate
  };
}

// Additional utility for frame rate throttling
export function useThrottledAnimation(callback: (time: number) => void, fps: number = 30) {
  const callbackRef = useRef(callback);
  const frameRef = useRef<number>();
  const lastTimeRef = useRef(0);

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const animate = (currentTime: number) => {
    const frameInterval = 1000 / fps;
    
    if (currentTime - lastTimeRef.current >= frameInterval) {
      callbackRef.current(currentTime);
      lastTimeRef.current = currentTime;
    }
    
    frameRef.current = requestAnimationFrame(animate);
  };

  const start = () => {
    if (!frameRef.current) {
      frameRef.current = requestAnimationFrame(animate);
    }
  };

  const stop = () => {
    if (frameRef.current) {
      cancelAnimationFrame(frameRef.current);
      frameRef.current = undefined;
    }
  };

  return { start, stop };
}
