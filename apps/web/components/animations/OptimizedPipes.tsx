'use client';

import { useRef } from 'react';
import { usePerformanceOptimization } from './hooks/usePerformanceOptimization';

export function OptimizedPipes() {
  const containerRef = useRef<HTMLDivElement>(null);
  const { shouldAnimate } = usePerformanceOptimization(containerRef);

  if (!shouldAnimate) {
    return (
      <div ref={containerRef} className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Static version with minimal visual elements */}
        <div className="absolute top-1/4 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent" />
        <div className="absolute top-3/4 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-purple-400/15 to-transparent" />
      </div>
    );
  }

  return (
    <div ref={containerRef} className="absolute inset-0 overflow-hidden pointer-events-none">
      <style jsx>{`
        @keyframes flowRight {
          0% { transform: translateX(-100px) translateZ(0); opacity: 0; }
          10% { opacity: 1; }
          90% { opacity: 1; }
          100% { transform: translateX(calc(100vw + 100px)) translateZ(0); opacity: 0; }
        }
        
        @keyframes pulse {
          0%, 100% { transform: scale(1) translateZ(0); opacity: 0.4; }
          50% { transform: scale(1.2) translateZ(0); opacity: 0.8; }
        }
        
        .pipe-line {
          will-change: transform;
          transform: translateZ(0);
        }
        
        .flow-particle {
          will-change: transform, opacity;
          transform: translateZ(0);
          animation: flowRight 8s linear infinite;
        }
        
        .pulse-node {
          will-change: transform, opacity;
          transform: translateZ(0);
          animation: pulse 2s ease-in-out infinite;
        }
      `}</style>

      {/* Main pipe lines using CSS gradients */}
      <div className="pipe-line absolute top-1/4 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-400/30 to-transparent opacity-60" />
      <div className="pipe-line absolute top-3/4 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-purple-400/25 to-transparent opacity-40" />

      {/* Flowing particles using CSS animations */}
      <div 
        className="flow-particle absolute top-1/4 w-2 h-2 bg-blue-400 rounded-full"
        style={{ 
          animationDelay: '0s',
          boxShadow: '0 0 8px rgba(0, 212, 255, 0.6)'
        }}
      />
      <div 
        className="flow-particle absolute top-1/4 w-1.5 h-1.5 bg-green-400 rounded-full"
        style={{ 
          animationDelay: '2s',
          boxShadow: '0 0 6px rgba(57, 255, 20, 0.6)'
        }}
      />
      <div 
        className="flow-particle absolute top-3/4 w-1.5 h-1.5 bg-purple-400 rounded-full"
        style={{ 
          animationDelay: '4s',
          boxShadow: '0 0 6px rgba(139, 69, 255, 0.6)'
        }}
      />

      {/* Pulsing nodes */}
      <div 
        className="pulse-node absolute w-3 h-3 bg-blue-400/50 rounded-full"
        style={{ 
          top: 'calc(25% - 6px)', 
          left: '20%',
          boxShadow: '0 0 12px rgba(0, 212, 255, 0.4)'
        }}
      />
      <div 
        className="pulse-node absolute w-2.5 h-2.5 bg-purple-400/50 rounded-full"
        style={{ 
          top: 'calc(75% - 5px)', 
          left: '60%',
          animationDelay: '1s',
          boxShadow: '0 0 10px rgba(139, 69, 255, 0.4)'
        }}
      />
    </div>
  );
}
