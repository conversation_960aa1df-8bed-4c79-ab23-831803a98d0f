/* Optimized CSS animations to replace expensive SVG filters */

.pipe-glow {
  filter: drop-shadow(0 0 4px currentColor);
}

.particle-glow {
  filter: drop-shadow(0 0 6px currentColor);
}

/* Hardware-accelerated pulse animation */
@keyframes pulse {
  0%, 100% { 
    transform: scale(1) translateZ(0); 
    opacity: 0.4; 
  }
  50% { 
    transform: scale(1.3) translateZ(0); 
    opacity: 0.8; 
  }
}

/* Optimized fade animation */
@keyframes fadeInOut {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.8; }
}

/* Performance-optimized particle movement */
.optimized-particle {
  will-change: transform, opacity;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Reduced motion fallback */
@media (prefers-reduced-motion: reduce) {
  .pipe-glow,
  .particle-glow {
    filter: none;
  }
  
  .optimized-particle {
    animation: none !important;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Low power mode optimizations */
@media (max-resolution: 1dppx) {
  .pipe-glow,
  .particle-glow {
    filter: none;
  }
}
