'use client';

import { useRef } from 'react';
import { usePerformanceOptimization } from './hooks/usePerformanceOptimization';
import './animations.css';

export function AnimatedPipes() {
  const svgRef = useRef<SVGSVGElement>(null);
  const { shouldAnimate } = usePerformanceOptimization(svgRef);

  // Don't render animations if shouldn't animate
  if (!shouldAnimate) {
    return (
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <svg
          ref={svgRef}
          className="absolute inset-0 w-full h-full"
          viewBox="0 0 1920 1080"
          preserveAspectRatio="xMidYMid slice"
        >
          {/* Static version - just the pipes without animations or filters */}
          <path
            d="M-100,200 Q200,150 400,300 T800,250 Q1200,200 1600,350 T2100,300"
            stroke="rgba(0, 212, 255, 0.2)"
            strokeWidth="8"
            fill="none"
            className="opacity-40"
          />
          <path
            d="M-100,600 Q300,550 600,650 T1000,600 Q1400,550 1800,700 T2200,650"
            stroke="rgba(139, 69, 255, 0.15)"
            strokeWidth="6"
            fill="none"
            className="opacity-30"
          />
        </svg>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <svg
        ref={svgRef}
        className="absolute inset-0 w-full h-full"
        viewBox="0 0 1920 1080"
        preserveAspectRatio="xMidYMid slice"
      >
        <defs>
          {/* Simplified Gradient Definitions */}
          <linearGradient id="pipeGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgba(0, 212, 255, 0.25)" />
            <stop offset="100%" stopColor="rgba(139, 69, 255, 0.25)" />
          </linearGradient>

          <linearGradient id="pipeGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="rgba(139, 69, 255, 0.15)" />
            <stop offset="100%" stopColor="rgba(57, 255, 20, 0.15)" />
          </linearGradient>

          {/* Lightweight glow using CSS box-shadow instead of SVG filters */}
          <style>{`
            .pipe-glow {
              filter: drop-shadow(0 0 4px currentColor);
            }
            .particle-glow {
              filter: drop-shadow(0 0 6px currentColor);
            }
          `}</style>

          {/* Animation Paths */}
          <path id="mainPath1" d="M-100,200 Q200,150 400,300 T800,250 Q1200,200 1600,350 T2100,300" />
          <path id="mainPath2" d="M-100,600 Q300,550 600,650 T1000,600 Q1400,550 1800,700 T2200,650" />
        </defs>

        {/* Main Horizontal Pipes - Using CSS glow instead of SVG filters */}
        <path
          d="M-100,200 Q200,150 400,300 T800,250 Q1200,200 1600,350 T2100,300"
          stroke="url(#pipeGradient1)"
          strokeWidth="10"
          fill="none"
          className="opacity-50 pipe-glow"
          style={{ color: 'rgba(0, 212, 255, 0.3)' }}
        />

        <path
          d="M-100,600 Q300,550 600,650 T1000,600 Q1400,550 1800,700 T2200,650"
          stroke="url(#pipeGradient2)"
          strokeWidth="6"
          fill="none"
          className="opacity-35 pipe-glow"
          style={{ color: 'rgba(139, 69, 255, 0.2)' }}
        />

        {/* Simplified Animated Particles - Only 2 particles instead of 6 */}
        <circle
          className="particle-glow"
          r="3"
          fill="rgba(0, 212, 255, 0.7)"
          style={{ color: 'rgba(0, 212, 255, 0.5)' }}
        >
          <animateMotion dur="12s" repeatCount="indefinite">
            <mpath xlinkHref="#mainPath1" />
          </animateMotion>
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite" />
        </circle>

        <circle
          className="particle-glow"
          r="2.5"
          fill="rgba(139, 69, 255, 0.7)"
          style={{ color: 'rgba(139, 69, 255, 0.5)' }}
        >
          <animateMotion dur="15s" repeatCount="indefinite" begin="3s">
            <mpath xlinkHref="#mainPath2" />
          </animateMotion>
          <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.5s" repeatCount="indefinite" />
        </circle>

        {/* Simplified Node Points - Only 2 nodes with CSS animations */}
        <circle
          cx="400"
          cy="300"
          r="6"
          fill="rgba(0, 212, 255, 0.4)"
          className="particle-glow"
          style={{
            color: 'rgba(0, 212, 255, 0.3)',
            animation: 'pulse 2s ease-in-out infinite'
          }}
        />

        <circle
          cx="1000"
          cy="600"
          r="5"
          fill="rgba(139, 69, 255, 0.4)"
          className="particle-glow"
          style={{
            color: 'rgba(139, 69, 255, 0.3)',
            animation: 'pulse 2.5s ease-in-out infinite 0.5s'
          }}
        />

        {/* CSS Keyframes for pulse animation */}
        <style>{`
          @keyframes pulse {
            0%, 100% {
              transform: scale(1);
              opacity: 0.4;
            }
            50% {
              transform: scale(1.3);
              opacity: 0.8;
            }
          }
        `}</style>
      </svg>
    </div>
  );
}