'use client';

import { HeroSection } from '@/components/sections/HeroSection';
import { ProblemSolutionSection } from '@/components/sections/ProblemSolutionSection';
import { ProductShowcaseSection } from '@/components/sections/ProductShowcaseSection';
import { BenefitsSection } from '@/components/sections/BenefitsSection';
import { SocialProofSection } from '@/components/sections/SocialProofSection';
import { FAQSection } from '@/components/sections/FAQSection';
import { FooterSection } from '@/components/sections/FooterSection';
import { AnimatedPipes } from '@/components/animations/AnimatedPipes';
import { ParticleSystem } from '@/components/animations/ParticleSystem';

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 text-white overflow-x-hidden relative">
      {/* Background Effects */}
      <div className="fixed inset-0 z-0">
        {/*TODO-MAIT GPU/CPU issues fix it*/}
        <ParticleSystem />
        <AnimatedPipes />
      </div>

      {/* Content Sections */}
      <div className="relative z-10">
        <HeroSection />
        <ProblemSolutionSection />
        <ProductShowcaseSection />
        <BenefitsSection />
        <SocialProofSection />
        <FAQSection />
        <FooterSection />
      </div>
    </main>
  );
}